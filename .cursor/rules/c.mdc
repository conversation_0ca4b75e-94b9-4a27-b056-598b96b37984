---
alwaysApply: false
---
---
title: Embedded Development Rules
description: Rules for AI assistance in embedded development projects.
---

# Embedded Development Rules for Cursor AI

These rules guide the Cursor AI in providing assistance for embedded development projects, ensuring adherence to best practices and project conventions.

- **Respond in Chinese.**
  始终使用中文进行交流。

- **Analyze the project's existing code, libraries, and frameworks before making changes.**
  在进行任何修改之前，必须先分析项目现有的代码、库和框架，以确保一致性。

- **Prioritize C/C++ for performance-critical code, especially for hardware interaction and real-time tasks.**
  对于性能至关重要的代码，特别是硬件交互和实时任务，优先使用 C/C++。

- **Use Python for scripting, automation, and high-level logic where performance is not the primary concern.**
  在性能不是主要问题的场景下，使用 Python 进行脚本编写、自动化和高级逻辑控制。

- **When working with build systems, prefer CMake for its flexibility and widespread support in embedded environments.**
  在处理构建系统时，优先选择 CMake，因为它在嵌入式环境中具有灵活性和广泛的支持。

- **If PlatformIO is detected in the project, follow its structure and conventions for library management and board configurations.**
  如果项目中检测到 PlatformIO，则遵循其结构和约定进行库管理和板级配置。

- **For Arduino-based projects, adhere to the Arduino framework and its coding style.**
  对于基于 Arduino 的项目，遵循 Arduino 框架及其编码风格。

- **Follow the Google C++ Style Guide for C++ code to maintain consistency and readability.**
  对于 C++ 代码，遵循 Google C++ 风格指南，以保持一致性和可读性。

- **Write clear, concise, and well-commented code, especially for hardware-related configurations and low-level drivers.**
  编写清晰、简洁且注释良好的代码，特别是对于硬件相关配置和底层驱动程序。

- **Ensure that code is memory-efficient and avoids dynamic memory allocation where possible.**
  确保代码内存高效，并尽可能避免动态内存分配。

- **When adding new features, include corresponding unit tests to ensure code quality and stability.**
  在添加新功能时，包含相应的单元测试，以确保代码质量和稳定性。

