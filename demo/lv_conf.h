/**
 * @file lv_conf.h
 * LVGL配置文件 - 最简ESP32+GC9A01配置
 */

#ifndef LV_CONF_H
#define LV_CONF_H

/* 启用LVGL */
#define LV_CONF_INCLUDE_SIMPLE      1

/*====================
   基础配置
 *====================*/

/* 颜色深度 */
#define LV_COLOR_DEPTH              16

/* 字节序配置 - 关键配置 */
#define LV_COLOR_16_SWAP            1    /* 启用字节交换 */

/* 内存配置 */
#define LV_MEM_CUSTOM               0
#define LV_MEM_SIZE                 (32U * 1024U)  /* 32KB内存池 */

/* 显示刷新配置 */
#define LV_DISP_DEF_REFR_PERIOD     30
#define LV_INDEV_DEF_READ_PERIOD    30

/*====================
   基础组件启用
 *====================*/

/* 基础组件 */
#define LV_USE_ARC                  1
#define LV_USE_IMG                  1
#define LV_USE_LABEL                1

/* 字体 */
#define LV_FONT_MONTSERRAT_14       1

/* 动画 */
#define LV_USE_ANIMATION            1

/*====================
   调试配置
 *====================*/

/* 日志配置 */
#define LV_USE_LOG                  1
#define LV_LOG_LEVEL                LV_LOG_LEVEL_WARN
#define LV_LOG_PRINTF               1

/* 断言 */
#define LV_USE_ASSERT_NULL          1
#define LV_USE_ASSERT_MALLOC        1

#endif /*LV_CONF_H*/