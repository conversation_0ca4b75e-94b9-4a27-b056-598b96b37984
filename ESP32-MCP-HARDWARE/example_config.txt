# ESP32-MCP-HARDWARE 配置示例
# 复制此文件内容到 idf.py menuconfig 中进行配置

# ==================== WiFi配置 ====================
# 在 menuconfig 中的路径：
# ESP32 MCP Hardware Configuration -> WiFi Configuration

WiFi SSID: "Your_WiFi_Network_Name"
WiFi Password: "Your_WiFi_Password"
Maximum retry: 5

# ==================== MCP配置 ====================
# 在 menuconfig 中的路径：
# ESP32 MCP Hardware Configuration -> MCP Configuration

MCP WebSocket Endpoint: "wss://api.xiaozhi.me/mcp/?token=your_access_token"
MCP Reconnect Interval (ms): 5000
MCP Ping Interval (ms): 30000
MCP Message Buffer Size: 2048

# ==================== 硬件引脚配置 ====================
# 在 menuconfig 中的路径：
# ESP32 MCP Hardware Configuration -> Hardware Pin Configuration

SPI Clock Pin: 19
SPI MOSI Pin: 20
Display DC Pin: 21
Display Reset Pin: 1
Left Display CS Pin: 2
Right Display CS Pin: 45
Status LED Pin: 2

# ==================== 眼睛动画配置 ====================
# 在 menuconfig 中的路径：
# ESP32 MCP Hardware Configuration -> Eye Animation Configuration

Minimum Blink Interval (ms): 3000
Maximum Blink Interval (ms): 6000
Minimum Look Movement Interval (ms): 4000
Maximum Look Movement Interval (ms): 10000
Minimum Emotion Change Interval (ms): 10000
Maximum Emotion Change Interval (ms): 20000
Animation Timer Period (ms): 200

# ==================== 获取小智MCP访问令牌 ====================

1. 访问小智AI官网：https://xiaozhi.me
2. 注册并登录账户
3. 进入开发者设置
4. 创建新的MCP端点
5. 复制生成的WebSocket URL（包含token）
6. 将URL配置到 MCP WebSocket Endpoint 中

示例URL格式：
wss://api.xiaozhi.me/mcp/?token=eyJhbGciOiJFUzI1NiIsInR5cCI6IkpXVCJ9...

# ==================== 硬件连接示例 ====================

ESP32 引脚连接：

左屏 GC9A01:
VCC  -> 3.3V
GND  -> GND
SCL  -> GPIO 19 (SCK)
SDA  -> GPIO 20 (MOSI)
RES  -> GPIO 1  (RST, 共享)
DC   -> GPIO 21 (DC, 共享)
CS   -> GPIO 2  (CS_LEFT)
BLK  -> 3.3V (背光)

右屏 GC9A01:
VCC  -> 3.3V
GND  -> GND
SCL  -> GPIO 19 (SCK, 共享)
SDA  -> GPIO 20 (MOSI, 共享)
RES  -> GPIO 1  (RST, 共享)
DC   -> GPIO 21 (DC, 共享)
CS   -> GPIO 45 (CS_RIGHT)
BLK  -> 3.3V (背光)

状态LED:
LED+ -> GPIO 2 (板载LED)
LED- -> GND

# ==================== 快速配置步骤 ====================

1. 进入项目目录：
   cd ESP32-MCP-HARDWARE

2. 打开配置菜单：
   idf.py menuconfig

3. 配置WiFi：
   ESP32 MCP Hardware Configuration -> WiFi Configuration
   - 设置 WiFi SSID
   - 设置 WiFi Password

4. 配置MCP：
   ESP32 MCP Hardware Configuration -> MCP Configuration
   - 设置 MCP WebSocket Endpoint（包含token）

5. 确认硬件引脚（如需修改）：
   ESP32 MCP Hardware Configuration -> Hardware Pin Configuration

6. 保存并退出配置

7. 编译项目：
   idf.py build

8. 烧录到ESP32：
   idf.py -p /dev/ttyUSB0 flash

9. 监控输出：
   idf.py -p /dev/ttyUSB0 monitor

# ==================== 故障排除 ====================

如果遇到问题，请检查：

1. ESP-IDF版本（推荐v5.0+）
2. 硬件连接是否正确
3. WiFi配置是否正确
4. MCP token是否有效
5. 串口输出的错误信息

更多帮助请参考 README.md 文件。
