#!/bin/bash

# ESP32-MCP-HARDWARE 构建脚本
# 用于快速编译、烧录和监控项目

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查ESP-IDF环境
check_idf_env() {
    if [ -z "$IDF_PATH" ]; then
        print_error "ESP-IDF环境未设置，请先运行: . \$IDF_PATH/export.sh"
        exit 1
    fi
    
    if ! command -v idf.py &> /dev/null; then
        print_error "idf.py命令未找到，请检查ESP-IDF安装"
        exit 1
    fi
    
    print_success "ESP-IDF环境检查通过"
}

# 显示帮助信息
show_help() {
    echo "ESP32-MCP-HARDWARE 构建脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  build       编译项目"
    echo "  flash       烧录到设备"
    echo "  monitor     监控串口输出"
    echo "  clean       清理构建文件"
    echo "  menuconfig  打开配置菜单"
    echo "  all         编译并烧录"
    echo "  help        显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 build          # 仅编译"
    echo "  $0 flash          # 仅烧录"
    echo "  $0 all            # 编译并烧录"
    echo "  $0 -p /dev/ttyUSB0 flash  # 指定端口烧录"
}

# 默认参数
PORT=""
ACTION=""

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -p|--port)
            PORT="$2"
            shift 2
            ;;
        build|flash|monitor|clean|menuconfig|all|help)
            ACTION="$1"
            shift
            ;;
        *)
            print_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 如果没有指定动作，显示帮助
if [ -z "$ACTION" ]; then
    show_help
    exit 0
fi

# 检查环境
check_idf_env

# 构建端口参数
PORT_ARG=""
if [ -n "$PORT" ]; then
    PORT_ARG="-p $PORT"
fi

# 执行相应动作
case $ACTION in
    build)
        print_info "开始编译项目..."
        idf.py build
        print_success "编译完成"
        ;;
    
    flash)
        print_info "开始烧录到设备..."
        if [ -n "$PORT" ]; then
            print_info "使用端口: $PORT"
        fi
        idf.py $PORT_ARG flash
        print_success "烧录完成"
        ;;
    
    monitor)
        print_info "开始监控串口输出..."
        if [ -n "$PORT" ]; then
            print_info "使用端口: $PORT"
        fi
        print_info "按 Ctrl+] 退出监控"
        idf.py $PORT_ARG monitor
        ;;
    
    clean)
        print_info "清理构建文件..."
        idf.py clean
        print_success "清理完成"
        ;;
    
    menuconfig)
        print_info "打开配置菜单..."
        idf.py menuconfig
        ;;
    
    all)
        print_info "开始编译项目..."
        idf.py build
        print_success "编译完成"
        
        print_info "开始烧录到设备..."
        if [ -n "$PORT" ]; then
            print_info "使用端口: $PORT"
        fi
        idf.py $PORT_ARG flash
        print_success "烧录完成"
        
        print_info "开始监控串口输出..."
        print_info "按 Ctrl+] 退出监控"
        idf.py $PORT_ARG monitor
        ;;
    
    help)
        show_help
        ;;
    
    *)
        print_error "未知动作: $ACTION"
        show_help
        exit 1
        ;;
esac
