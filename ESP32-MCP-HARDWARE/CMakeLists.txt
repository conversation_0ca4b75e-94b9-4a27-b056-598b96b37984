# ESP32-MCP-HARDWARE 项目 CMakeLists.txt
# 小智 ESP32 MCP 硬件系统 - 双屏模拟眼睛效果 + MCP连接

cmake_minimum_required(VERSION 3.16)

# 设置项目信息
set(PROJECT_NAME "esp32_mcp_hardware")
set(PROJECT_VER "1.0.0")

# 包含ESP-IDF构建系统
include($ENV{IDF_PATH}/tools/cmake/project.cmake)

# 定义项目
project(${PROJECT_NAME})

# 设置编译选项
target_compile_options(${CMAKE_PROJECT_NAME}.elf PRIVATE
    -Wno-format
    -Wno-unused-function
    -Wno-unused-variable
)
