/**
 * @file mcp_client.c
 * @brief MCP WebSocket客户端实现
 * 
 * ESP-IDF版本的WebSocket MCP客户端
 * 基于esp_websocket_client实现
 */

#include "mcp_client.h"

static const char *TAG = "MCP_CLIENT";

// 全局MCP客户端实例
static mcp_client_t g_mcp_client = {0};

// 回调函数
static mcp_connection_callback_t g_connection_callback = NULL;
static mcp_message_callback_t g_message_callback = NULL;
static mcp_error_callback_t g_error_callback = NULL;

// 前向声明
static void mcp_websocket_event_handler(void *handler_args, esp_event_base_t base, int32_t event_id, void *event_data);
static void mcp_client_task(void *pvParameters);
static esp_err_t mcp_handle_message(const char* data, int data_len);
static esp_err_t mcp_send_tools_list(void);
static esp_err_t mcp_handle_tool_call(cJSON *json);
static char* mcp_escape_json_string(const char* input);

esp_err_t mcp_client_init(const char* endpoint)
{
    if (!endpoint) {
        ESP_LOGE(TAG, "Endpoint cannot be NULL");
        return ESP_ERR_INVALID_ARG;
    }

    // 初始化客户端结构
    memset(&g_mcp_client, 0, sizeof(mcp_client_t));
    strncpy(g_mcp_client.endpoint, endpoint, sizeof(g_mcp_client.endpoint) - 1);
    g_mcp_client.state = MCP_STATE_DISCONNECTED;
    g_mcp_client.auto_reconnect = true;
    g_mcp_client.request_id = 1;

    // 创建事件组
    g_mcp_client.event_group = xEventGroupCreate();
    if (!g_mcp_client.event_group) {
        ESP_LOGE(TAG, "Failed to create event group");
        return ESP_ERR_NO_MEM;
    }

    // 配置WebSocket客户端
    esp_websocket_client_config_t websocket_cfg = {
        .uri = g_mcp_client.endpoint,
        .buffer_size = MCP_BUFFER_SIZE,
        .ping_interval_sec = MCP_PING_INTERVAL / 1000,
    };

    g_mcp_client.client = esp_websocket_client_init(&websocket_cfg);
    if (!g_mcp_client.client) {
        ESP_LOGE(TAG, "Failed to initialize websocket client");
        vEventGroupDelete(g_mcp_client.event_group);
        return ESP_ERR_NO_MEM;
    }

    // 注册事件处理器
    esp_websocket_register_events(g_mcp_client.client, WEBSOCKET_EVENT_ANY, mcp_websocket_event_handler, NULL);

    ESP_LOGI(TAG, "MCP client initialized with endpoint: %s", endpoint);
    return ESP_OK;
}

esp_err_t mcp_client_start(void)
{
    if (g_mcp_client.state != MCP_STATE_DISCONNECTED) {
        ESP_LOGW(TAG, "Client already started");
        return ESP_OK;
    }

    g_mcp_client.state = MCP_STATE_CONNECTING;

    // 启动WebSocket客户端
    esp_err_t ret = esp_websocket_client_start(g_mcp_client.client);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start websocket client: %s", esp_err_to_name(ret));
        g_mcp_client.state = MCP_STATE_ERROR;
        return ret;
    }

    // 创建客户端任务
    BaseType_t task_ret = xTaskCreate(mcp_client_task, "mcp_client", 4096, NULL, 5, &g_mcp_client.task_handle);
    if (task_ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create MCP client task");
        esp_websocket_client_stop(g_mcp_client.client);
        g_mcp_client.state = MCP_STATE_ERROR;
        return ESP_ERR_NO_MEM;
    }

    ESP_LOGI(TAG, "MCP client started");
    return ESP_OK;
}

void mcp_client_stop(void)
{
    if (g_mcp_client.state == MCP_STATE_DISCONNECTED) {
        return;
    }

    g_mcp_client.auto_reconnect = false;
    g_mcp_client.state = MCP_STATE_DISCONNECTED;

    // 停止WebSocket客户端
    if (g_mcp_client.client) {
        esp_websocket_client_stop(g_mcp_client.client);
        esp_websocket_client_destroy(g_mcp_client.client);
        g_mcp_client.client = NULL;
    }

    // 删除任务
    if (g_mcp_client.task_handle) {
        vTaskDelete(g_mcp_client.task_handle);
        g_mcp_client.task_handle = NULL;
    }

    // 删除事件组
    if (g_mcp_client.event_group) {
        vEventGroupDelete(g_mcp_client.event_group);
        g_mcp_client.event_group = NULL;
    }

    ESP_LOGI(TAG, "MCP client stopped");
}

esp_err_t mcp_client_register_tool(const char* name, 
                                   const char* description,
                                   const char* input_schema,
                                   mcp_tool_callback_t callback)
{
    if (!name || !description || !input_schema || !callback) {
        return ESP_ERR_INVALID_ARG;
    }

    if (g_mcp_client.tool_count >= MCP_MAX_TOOLS) {
        ESP_LOGE(TAG, "Maximum number of tools reached");
        return ESP_ERR_NO_MEM;
    }

    // 检查工具是否已存在
    for (int i = 0; i < g_mcp_client.tool_count; i++) {
        if (g_mcp_client.tools[i].active && strcmp(g_mcp_client.tools[i].name, name) == 0) {
            ESP_LOGW(TAG, "Tool '%s' already registered", name);
            return ESP_ERR_INVALID_STATE;
        }
    }

    // 找到空闲槽位
    int slot = -1;
    for (int i = 0; i < MCP_MAX_TOOLS; i++) {
        if (!g_mcp_client.tools[i].active) {
            slot = i;
            break;
        }
    }

    if (slot == -1) {
        ESP_LOGE(TAG, "No available tool slot");
        return ESP_ERR_NO_MEM;
    }

    // 注册工具
    mcp_tool_t *tool = &g_mcp_client.tools[slot];
    strncpy(tool->name, name, sizeof(tool->name) - 1);
    strncpy(tool->description, description, sizeof(tool->description) - 1);
    strncpy(tool->input_schema, input_schema, sizeof(tool->input_schema) - 1);
    tool->callback = callback;
    tool->active = true;

    if (slot >= g_mcp_client.tool_count) {
        g_mcp_client.tool_count = slot + 1;
    }

    ESP_LOGI(TAG, "Tool '%s' registered successfully", name);

    // 如果已连接，发送工具列表更新
    if (g_mcp_client.state == MCP_STATE_CONNECTED) {
        mcp_send_tools_list();
    }

    return ESP_OK;
}

esp_err_t mcp_client_unregister_tool(const char* name)
{
    if (!name) {
        return ESP_ERR_INVALID_ARG;
    }

    for (int i = 0; i < g_mcp_client.tool_count; i++) {
        if (g_mcp_client.tools[i].active && strcmp(g_mcp_client.tools[i].name, name) == 0) {
            g_mcp_client.tools[i].active = false;
            ESP_LOGI(TAG, "Tool '%s' unregistered", name);

            // 如果已连接，发送工具列表更新
            if (g_mcp_client.state == MCP_STATE_CONNECTED) {
                mcp_send_tools_list();
            }
            return ESP_OK;
        }
    }

    ESP_LOGW(TAG, "Tool '%s' not found", name);
    return ESP_ERR_NOT_FOUND;
}

esp_err_t mcp_client_send_message(const char* message)
{
    if (!message || g_mcp_client.state != MCP_STATE_CONNECTED) {
        return ESP_ERR_INVALID_STATE;
    }

    int ret = esp_websocket_client_send_text(g_mcp_client.client, message, strlen(message), portMAX_DELAY);
    if (ret < 0) {
        ESP_LOGE(TAG, "Failed to send message");
        return ESP_FAIL;
    }

    ESP_LOGD(TAG, "Message sent: %s", message);
    return ESP_OK;
}

mcp_client_state_t mcp_client_get_state(void)
{
    return g_mcp_client.state;
}

int mcp_client_get_tool_count(void)
{
    int count = 0;
    for (int i = 0; i < g_mcp_client.tool_count; i++) {
        if (g_mcp_client.tools[i].active) {
            count++;
        }
    }
    return count;
}

bool mcp_client_is_connected(void)
{
    return g_mcp_client.state == MCP_STATE_CONNECTED;
}

void mcp_client_set_connection_callback(mcp_connection_callback_t callback)
{
    g_connection_callback = callback;
}

void mcp_client_set_message_callback(mcp_message_callback_t callback)
{
    g_message_callback = callback;
}

void mcp_client_set_error_callback(mcp_error_callback_t callback)
{
    g_error_callback = callback;
}

mcp_tool_response_t mcp_create_response(const char* content, bool is_error)
{
    mcp_tool_response_t response = {0};
    if (content) {
        response.content = strdup(content);
        response.is_error = is_error;
    }
    return response;
}

void mcp_free_response(mcp_tool_response_t* response)
{
    if (response && response->content) {
        free(response->content);
        response->content = NULL;
    }
}

// WebSocket事件处理器
static void mcp_websocket_event_handler(void *handler_args, esp_event_base_t base, int32_t event_id, void *event_data)
{
    esp_websocket_event_data_t *data = (esp_websocket_event_data_t *)event_data;

    switch (event_id) {
        case WEBSOCKET_EVENT_CONNECTED:
            ESP_LOGI(TAG, "WebSocket connected");
            g_mcp_client.state = MCP_STATE_CONNECTED;
            xEventGroupSetBits(g_mcp_client.event_group, MCP_CONNECTED_BIT);

            // 发送工具列表
            mcp_send_tools_list();

            if (g_connection_callback) {
                g_connection_callback(true);
            }
            break;

        case WEBSOCKET_EVENT_DISCONNECTED:
            ESP_LOGI(TAG, "WebSocket disconnected");
            g_mcp_client.state = MCP_STATE_DISCONNECTED;
            xEventGroupSetBits(g_mcp_client.event_group, MCP_DISCONNECTED_BIT);

            if (g_connection_callback) {
                g_connection_callback(false);
            }
            break;

        case WEBSOCKET_EVENT_DATA:
            if (data->op_code == 0x01) { // Text frame
                mcp_handle_message(data->data_ptr, data->data_len);
            }
            break;

        case WEBSOCKET_EVENT_ERROR:
            ESP_LOGE(TAG, "WebSocket error");
            g_mcp_client.state = MCP_STATE_ERROR;
            xEventGroupSetBits(g_mcp_client.event_group, MCP_ERROR_BIT);

            if (g_error_callback) {
                g_error_callback("WebSocket connection error");
            }
            break;

        default:
            break;
    }
}

// MCP客户端任务
static void mcp_client_task(void *pvParameters)
{
    ESP_LOGI(TAG, "MCP client task started");

    while (g_mcp_client.auto_reconnect) {
        EventBits_t bits = xEventGroupWaitBits(g_mcp_client.event_group,
                                               MCP_CONNECTED_BIT | MCP_DISCONNECTED_BIT | MCP_ERROR_BIT,
                                               pdTRUE, pdFALSE, portMAX_DELAY);

        if (bits & MCP_CONNECTED_BIT) {
            ESP_LOGI(TAG, "MCP connected successfully");
            g_mcp_client.last_ping_time = xTaskGetTickCount() * portTICK_PERIOD_MS;
        }

        if (bits & (MCP_DISCONNECTED_BIT | MCP_ERROR_BIT)) {
            if (g_mcp_client.auto_reconnect && g_mcp_client.state != MCP_STATE_CONNECTED) {
                ESP_LOGI(TAG, "Attempting to reconnect in %d ms", MCP_RECONNECT_INTERVAL);
                vTaskDelay(pdMS_TO_TICKS(MCP_RECONNECT_INTERVAL));

                if (g_mcp_client.client) {
                    g_mcp_client.state = MCP_STATE_CONNECTING;
                    esp_websocket_client_start(g_mcp_client.client);
                }
            }
        }
    }

    ESP_LOGI(TAG, "MCP client task ended");
    vTaskDelete(NULL);
}

// 处理接收到的消息
static esp_err_t mcp_handle_message(const char* data, int data_len)
{
    if (!data || data_len <= 0) {
        return ESP_ERR_INVALID_ARG;
    }

    // 创建以null结尾的字符串
    char *message = malloc(data_len + 1);
    if (!message) {
        ESP_LOGE(TAG, "Failed to allocate memory for message");
        return ESP_ERR_NO_MEM;
    }
    memcpy(message, data, data_len);
    message[data_len] = '\0';

    ESP_LOGD(TAG, "Received message: %s", message);

    // 解析JSON
    cJSON *json = cJSON_Parse(message);
    if (!json) {
        ESP_LOGE(TAG, "Failed to parse JSON message");
        free(message);
        return ESP_ERR_INVALID_ARG;
    }

    // 检查是否为工具调用
    cJSON *method = cJSON_GetObjectItem(json, "method");
    if (method && cJSON_IsString(method)) {
        if (strcmp(method->valuestring, "tools/call") == 0) {
            mcp_handle_tool_call(json);
        }
    }

    // 调用消息回调
    if (g_message_callback) {
        g_message_callback(message);
    }

    cJSON_Delete(json);
    free(message);
    return ESP_OK;
}

// 发送工具列表
static esp_err_t mcp_send_tools_list(void)
{
    cJSON *tools_list = cJSON_CreateObject();
    cJSON *jsonrpc = cJSON_CreateString("2.0");
    cJSON *method = cJSON_CreateString("tools/list");
    cJSON *id = cJSON_CreateNumber(g_mcp_client.request_id++);
    cJSON *result = cJSON_CreateObject();
    cJSON *tools = cJSON_CreateArray();

    cJSON_AddItemToObject(tools_list, "jsonrpc", jsonrpc);
    cJSON_AddItemToObject(tools_list, "method", method);
    cJSON_AddItemToObject(tools_list, "id", id);
    cJSON_AddItemToObject(tools_list, "result", result);
    cJSON_AddItemToObject(result, "tools", tools);

    // 添加已注册的工具
    for (int i = 0; i < g_mcp_client.tool_count; i++) {
        if (g_mcp_client.tools[i].active) {
            cJSON *tool = cJSON_CreateObject();
            cJSON *name = cJSON_CreateString(g_mcp_client.tools[i].name);
            cJSON *description = cJSON_CreateString(g_mcp_client.tools[i].description);

            // 解析输入schema
            cJSON *input_schema = cJSON_Parse(g_mcp_client.tools[i].input_schema);
            if (!input_schema) {
                input_schema = cJSON_CreateObject();
            }

            cJSON_AddItemToObject(tool, "name", name);
            cJSON_AddItemToObject(tool, "description", description);
            cJSON_AddItemToObject(tool, "inputSchema", input_schema);
            cJSON_AddItemToArray(tools, tool);
        }
    }

    char *json_string = cJSON_Print(tools_list);
    if (json_string) {
        esp_err_t ret = mcp_client_send_message(json_string);
        free(json_string);
        cJSON_Delete(tools_list);

        ESP_LOGI(TAG, "Tools list sent, %d tools registered", mcp_client_get_tool_count());
        return ret;
    }

    cJSON_Delete(tools_list);
    return ESP_ERR_NO_MEM;
}

// 处理工具调用
static esp_err_t mcp_handle_tool_call(cJSON *json)
{
    cJSON *params = cJSON_GetObjectItem(json, "params");
    if (!params) {
        ESP_LOGE(TAG, "No params in tool call");
        return ESP_ERR_INVALID_ARG;
    }

    cJSON *name = cJSON_GetObjectItem(params, "name");
    cJSON *arguments = cJSON_GetObjectItem(params, "arguments");
    cJSON *id = cJSON_GetObjectItem(json, "id");

    if (!name || !cJSON_IsString(name) || !id) {
        ESP_LOGE(TAG, "Invalid tool call format");
        return ESP_ERR_INVALID_ARG;
    }

    // 查找工具
    mcp_tool_t *tool = NULL;
    for (int i = 0; i < g_mcp_client.tool_count; i++) {
        if (g_mcp_client.tools[i].active && strcmp(g_mcp_client.tools[i].name, name->valuestring) == 0) {
            tool = &g_mcp_client.tools[i];
            break;
        }
    }

    if (!tool) {
        ESP_LOGE(TAG, "Tool '%s' not found", name->valuestring);
        return ESP_ERR_NOT_FOUND;
    }

    // 准备参数
    char *args_string = NULL;
    if (arguments) {
        args_string = cJSON_Print(arguments);
    } else {
        args_string = strdup("{}");
    }

    if (!args_string) {
        ESP_LOGE(TAG, "Failed to serialize arguments");
        return ESP_ERR_NO_MEM;
    }

    ESP_LOGI(TAG, "Calling tool '%s' with args: %s", name->valuestring, args_string);

    // 调用工具
    mcp_tool_response_t response = tool->callback(args_string);
    free(args_string);

    // 构建响应
    cJSON *response_json = cJSON_CreateObject();
    cJSON *jsonrpc = cJSON_CreateString("2.0");
    cJSON *response_id = cJSON_CreateNumber(cJSON_GetNumberValue(id));

    cJSON_AddItemToObject(response_json, "jsonrpc", jsonrpc);
    cJSON_AddItemToObject(response_json, "id", response_id);

    if (response.is_error) {
        cJSON *error = cJSON_CreateObject();
        cJSON *code = cJSON_CreateNumber(-32000);
        cJSON *message = cJSON_CreateString(response.content ? response.content : "Tool execution error");

        cJSON_AddItemToObject(error, "code", code);
        cJSON_AddItemToObject(error, "message", message);
        cJSON_AddItemToObject(response_json, "error", error);
    } else {
        cJSON *result = cJSON_CreateObject();
        cJSON *content = cJSON_CreateArray();
        cJSON *content_item = cJSON_CreateObject();
        cJSON *type = cJSON_CreateString("text");
        cJSON *text = cJSON_CreateString(response.content ? response.content : "{}");

        cJSON_AddItemToObject(content_item, "type", type);
        cJSON_AddItemToObject(content_item, "text", text);
        cJSON_AddItemToArray(content, content_item);
        cJSON_AddItemToObject(result, "content", content);
        cJSON_AddItemToObject(result, "isError", cJSON_CreateBool(false));
        cJSON_AddItemToObject(response_json, "result", result);
    }

    char *response_string = cJSON_Print(response_json);
    if (response_string) {
        esp_err_t ret = mcp_client_send_message(response_string);
        free(response_string);
        cJSON_Delete(response_json);
        mcp_free_response(&response);
        return ret;
    }

    cJSON_Delete(response_json);
    mcp_free_response(&response);
    return ESP_ERR_NO_MEM;
}

// JSON字符串转义
static char* mcp_escape_json_string(const char* input)
{
    if (!input) {
        return NULL;
    }

    size_t len = strlen(input);
    size_t escaped_len = len * 2 + 1; // 最坏情况下每个字符都需要转义
    char *escaped = malloc(escaped_len);
    if (!escaped) {
        return NULL;
    }

    size_t j = 0;
    for (size_t i = 0; i < len && j < escaped_len - 1; i++) {
        switch (input[i]) {
            case '"':
                escaped[j++] = '\\';
                escaped[j++] = '"';
                break;
            case '\\':
                escaped[j++] = '\\';
                escaped[j++] = '\\';
                break;
            case '\n':
                escaped[j++] = '\\';
                escaped[j++] = 'n';
                break;
            case '\r':
                escaped[j++] = '\\';
                escaped[j++] = 'r';
                break;
            case '\t':
                escaped[j++] = '\\';
                escaped[j++] = 't';
                break;
            default:
                escaped[j++] = input[i];
                break;
        }
    }
    escaped[j] = '\0';
    return escaped;
}
