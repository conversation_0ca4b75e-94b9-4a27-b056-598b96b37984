# 小智 ESP32 MCP 硬件系统

基于ESP-IDF的双屏模拟眼睛效果系统，支持MCP（Model Context Protocol）连接，可通过小智AI进行远程控制。

## 🌟 功能特性

### 🎭 双屏眼睛显示系统
- **双GC9A01圆形显示屏**：240x240分辨率，完美圆形眼球效果
- **动态眼球动画**：支持眨眼、眼球移动、情绪表达等多种动画
- **平滑动画过渡**：基于LVGL 9.3.0，无卡顿、无跳帧
- **多种情绪状态**：正常、开心、生气、惊讶、困倦等表情
- **自动随机动画**：自动眨眼、随机眼球移动、情绪变化

### 🔗 MCP连接系统
- **WebSocket MCP客户端**：支持与小智AI服务器连接
- **工具注册系统**：可扩展的硬件控制工具
- **远程控制**：通过MCP协议远程控制硬件设备
- **实时通信**：支持双向实时消息传输

### 🛠️ 已实现工具
1. **LED控制工具** (`led_control`)
   - 控制ESP32板载LED开关和闪烁
   - 参数：`state` - `"on"` / `"off"` / `"blink"`

2. **系统信息工具** (`system_info`)
   - 获取ESP32系统信息：芯片型号、内存、网络状态等
   - 无需参数

3. **眼睛控制工具** (`eye_control`)
   - 控制双屏眼睛显示效果
   - 支持眨眼、眼球移动、情绪切换
   - 参数：`action` - `"blink"` / `"look"` / `"emotion"`

4. **计算器工具** (`calculator`)
   - 简单数学计算（加减乘除）
   - 参数：`expression` - 如 `"10+5"` 或 `"20*3"`

## 🔧 硬件要求

### 必需硬件
- **ESP32开发板**（推荐ESP32-S3或ESP32-C3）
- **2个GC9A01圆形显示屏**（240x240分辨率）
- **连接线**

### 引脚连接
```
SPI总线（共享）：
- SCK  -> GPIO 19
- MOSI -> GPIO 20
- DC   -> GPIO 21
- RST  -> GPIO 1（共享）

显示屏片选：
- 左屏CS -> GPIO 2
- 右屏CS -> GPIO 45

状态指示：
- LED -> GPIO 2（板载LED）
```

## 📦 依赖组件

项目基于ESP-IDF框架，需要以下组件：
- `esp_wifi` - WiFi连接
- `esp_websocket_client` - WebSocket客户端
- `json` - JSON解析
- `lvgl` - 图形界面库
- `esp_lcd` - LCD驱动
- `driver` - GPIO/SPI驱动

## 🚀 快速开始

### 1. 环境准备
```bash
# 安装ESP-IDF（推荐v5.0+）
git clone --recursive https://github.com/espressif/esp-idf.git
cd esp-idf
./install.sh
. ./export.sh
```

### 2. 克隆项目
```bash
git clone <项目地址>
cd ESP32-MCP-HARDWARE
```

### 3. 配置项目
```bash
# 配置WiFi和MCP设置
idf.py menuconfig

# 在菜单中配置：
# ESP32 MCP Hardware Configuration ->
#   WiFi Configuration -> 设置WiFi SSID和密码
#   MCP Configuration -> 设置MCP服务器端点
#   Hardware Pin Configuration -> 确认引脚配置
```

### 4. 编译和烧录
```bash
# 编译项目
idf.py build

# 烧录到ESP32
idf.py -p /dev/ttyUSB0 flash

# 监控串口输出
idf.py -p /dev/ttyUSB0 monitor
```

## ⚙️ 配置选项

### WiFi配置
- `CONFIG_WIFI_SSID`: WiFi网络名称
- `CONFIG_WIFI_PASSWORD`: WiFi密码
- `CONFIG_WIFI_MAXIMUM_RETRY`: 最大重连次数

### MCP配置
- `CONFIG_MCP_ENDPOINT`: MCP WebSocket服务器地址
- `CONFIG_MCP_RECONNECT_INTERVAL`: 重连间隔（毫秒）
- `CONFIG_MCP_PING_INTERVAL`: 心跳间隔（毫秒）

### 硬件引脚配置
- `CONFIG_PIN_SCK`: SPI时钟引脚
- `CONFIG_PIN_MOSI`: SPI数据引脚
- `CONFIG_PIN_DC`: 显示屏DC引脚
- `CONFIG_PIN_RST`: 显示屏复位引脚
- `CONFIG_PIN_CS_LEFT`: 左屏片选引脚
- `CONFIG_PIN_CS_RIGHT`: 右屏片选引脚

### 眼睛动画配置
- `CONFIG_EYE_BLINK_INTERVAL_MIN/MAX`: 眨眼间隔范围
- `CONFIG_EYE_LOOK_INTERVAL_MIN/MAX`: 眼球移动间隔范围
- `CONFIG_EYE_EMOTION_INTERVAL_MIN/MAX`: 情绪变化间隔范围

## 💡 LED状态指示

系统通过板载LED显示连接状态：
- **快速闪烁（100ms）**：WiFi未连接
- **慢速闪烁（500ms）**：WiFi已连接，MCP未连接
- **常亮**：WiFi和MCP均已连接

## 🎮 MCP工具使用示例

### 控制LED
```json
{
  "jsonrpc": "2.0",
  "method": "tools/call",
  "params": {
    "name": "led_control",
    "arguments": {"state": "blink"}
  }
}
```

### 控制眼睛眨眼
```json
{
  "jsonrpc": "2.0",
  "method": "tools/call",
  "params": {
    "name": "eye_control",
    "arguments": {
      "action": "blink"
    }
  }
}
```

### 控制眼球移动
```json
{
  "jsonrpc": "2.0",
  "method": "tools/call",
  "params": {
    "name": "eye_control",
    "arguments": {
      "action": "look",
      "params": {"x": 10, "y": -5}
    }
  }
}
```

### 设置眼睛情绪
```json
{
  "jsonrpc": "2.0",
  "method": "tools/call",
  "params": {
    "name": "eye_control",
    "arguments": {
      "action": "emotion",
      "params": {"emotion": "happy"}
    }
  }
}
```

## 🔧 开发指南

### 添加新的MCP工具

1. 在`main.c`中定义工具回调函数：
```c
static mcp_tool_response_t my_tool(const char* args)
{
    // 解析参数
    cJSON *json = cJSON_Parse(args);

    // 执行操作
    // ...

    // 返回响应
    return mcp_create_response("{\"success\":true}", false);
}
```

2. 在`register_mcp_tools()`中注册工具：
```c
mcp_client_register_tool(
    "my_tool",
    "工具描述",
    "{\"properties\":{...},\"type\":\"object\"}",  // JSON Schema
    my_tool
);
```

### 自定义眼睛动画

可以通过以下API控制眼睛：
- `eye_set_emotion(emotion)` - 设置情绪
- `eye_trigger_blink()` - 触发眨眼
- `eye_look_at(x, y)` - 眼球移动
- `eye_get_state()` - 获取当前状态

### 修改硬件引脚

在`main/include/app_config.h`中修改引脚定义：
```c
#define PIN_SCK       19    // SPI时钟
#define PIN_MOSI      20    // SPI数据输出
#define PIN_DC        21    // 数据/命令控制
#define PIN_RST       1     // 复位引脚（共享）
#define PIN_CS_LEFT   2     // 左屏片选
#define PIN_CS_RIGHT  45    // 右屏片选
```

## 🐛 故障排除

### 常见问题

1. **编译错误：找不到LVGL头文件**
   - 确保已正确安装ESP-IDF v5.0+
   - 检查`idf.py menuconfig`中的组件配置

2. **显示屏无显示**
   - 检查SPI连接线
   - 确认引脚配置正确
   - 检查显示屏电源供应

3. **WiFi连接失败**
   - 检查WiFi SSID和密码配置
   - 确认WiFi信号强度
   - 查看串口输出的错误信息

4. **MCP连接失败**
   - 检查MCP服务器地址配置
   - 确认网络连接正常
   - 验证访问令牌有效性

### 调试技巧

1. **启用详细日志**：
   ```bash
   idf.py menuconfig
   # Component config -> Log output -> Default log verbosity -> Verbose
   ```

2. **监控内存使用**：
   - 系统会定期打印剩余内存信息
   - 注意内存泄漏问题

3. **检查任务状态**：
   - 使用`vTaskList()`查看任务状态
   - 监控任务堆栈使用情况

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 🤝 贡献

欢迎提交Issue和Pull Request！

### 贡献指南
1. Fork本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启Pull Request

## 📞 支持

如有问题，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者

## 🔗 相关项目

如果您对ESP32智能家居控制感兴趣，推荐关注：

### 🏠 [ha-esp32](https://gitee.com/panzuji/ha-esp32) - ESP32智能家居中枢

更完整的ESP32智能家居解决方案：
- **多平台对接**：支持小米、小度、涂鸦、天猫精灵等
- **HomeAssistant兼容**：在ESP32中实现HA核心功能
- **MCP接口**：提供标准MCP接口
- **统一控制**：一站式管理家庭智能设备

---

**🌟 如果这个项目对您有帮助，请给个Star支持一下！**
